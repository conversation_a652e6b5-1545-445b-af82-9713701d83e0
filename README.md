# S.N. Pvt. Industrial Training Institute Website

A modern, responsive website with comprehensive content management system for S.N. Pvt. Industrial Training Institute. Built with Next.js 15, TypeScript, Tailwind CSS, and Supabase.

## 🎯 Project Overview

This project is a complete rebuild of the SNPITC website (http://snpitc.in) featuring:

- **Modern responsive design** with mobile-first approach
- **Content Management System** with WYSIWYG editing
- **Admin dashboard** for easy content management
- **Secure authentication** system
- **SEO optimized** pages and content
- **Accessibility compliant** (WCAG 2.1 AA)

## 🚀 Features

### Frontend
- ✅ Responsive design with Tailwind CSS
- ✅ Modern UI components with TypeScript
- ✅ SEO-optimized pages with proper meta tags
- ✅ Accessibility features and semantic HTML
- ✅ Image optimization and lazy loading
- ✅ Smooth animations and transitions

### Content Management
- ✅ Admin authentication system
- ✅ WYSIWYG editor integration (TinyMCE ready)
- ✅ Page content management
- ✅ Media library for images and files
- ✅ Dynamic navigation management
- ✅ Contact form with submissions tracking

### Technical Stack
- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4, Custom CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: JWT with HTTP-only cookies
- **Editor**: TinyMCE (ready for integration)
- **Icons**: Lucide React
- **Deployment**: Vercel/Netlify ready

## 📋 Content Structure

The website includes all pages from the original site:

### Main Sections
- **Home** - Hero section, quick facts, introduction
- **About Us** - Institute info, introduction, schemes
- **Admissions** - Criteria, trades, application, fees
- **Facilities** - Infrastructure, labs, library, sports
- **Trainee** - Achievements, records, placements, results
- **Staff** - Faculty and administrative staff
- **More** - Industry linkages, activities, RTI, certificates
- **Gallery** - Photo gallery
- **Contact** - Contact information and form

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account (for database)

### 1. Clone the Repository
```bash
git clone https://github.com/Navsaharan/Sniti.git
cd Sniti
```

### 2. Install Dependencies
```bash
npm install
# or
yarn install
```

### 3. Environment Setup
Create a `.env.local` file in the root directory:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# JWT Secret for authentication
JWT_SECRET=your_jwt_secret_here_minimum_32_characters

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=S.N. Pvt. Industrial Training Institute
```

### 4. Database Setup
1. Create a new Supabase project
2. Run the SQL schema from `database-schema.sql` in your Supabase SQL editor
3. Update your environment variables with Supabase credentials

### 5. Run Development Server
```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) to view the website.

## 🔐 Admin Access

### Demo Credentials
- **URL**: http://localhost:3000/admin/login
- **Email**: <EMAIL>
- **Password**: admin123

### Admin Features
- Dashboard with site statistics
- Page content management
- Media library management
- User management
- Site settings configuration

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin panel pages
│   ├── api/               # API routes
│   ├── about/             # About pages
│   ├── contact/           # Contact page
│   └── ...                # Other public pages
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── ui/               # UI components
│   └── admin/            # Admin components
├── lib/                  # Utility libraries
│   ├── auth.ts           # Authentication utilities
│   ├── supabase.ts       # Database configuration
│   └── utils.ts          # Helper functions
└── middleware.ts         # Route protection
```

## 🎨 Customization

### Styling
- Modify `src/app/globals.css` for global styles
- Update `tailwind.config.js` for theme customization
- Component styles are in individual component files

### Content
- Edit page content through the admin panel
- Modify static content in component files
- Update navigation structure in `src/lib/utils.ts`

### Database
- Schema is defined in `database-schema.sql`
- Modify types in `src/lib/supabase.ts`
- Add new tables/fields as needed

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Netlify
1. Connect repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `.next`
4. Add environment variables

### Environment Variables for Production
Ensure all environment variables are set in your deployment platform:
- Supabase credentials
- JWT secret
- Site URL (production URL)

## 📚 Documentation

### For Administrators
- **Admin Guide**: Access `/admin` after login for content management
- **Page Editing**: Use the WYSIWYG editor to modify page content
- **Media Management**: Upload and organize images in the media library

### For Developers
- **API Documentation**: Check `/src/app/api` for available endpoints
- **Component Library**: Reusable components in `/src/components/ui`
- **Database Schema**: Full schema in `database-schema.sql`

## 🔧 Development

### Adding New Pages
1. Create page component in appropriate directory
2. Add route to navigation structure
3. Update database with page content
4. Test responsive design

### Adding New Features
1. Create components in `/src/components`
2. Add API routes if needed in `/src/app/api`
3. Update database schema if required
4. Test thoroughly

## 🐛 Troubleshooting

### Common Issues
1. **Database Connection**: Verify Supabase credentials
2. **Authentication**: Check JWT secret configuration
3. **Build Errors**: Ensure all dependencies are installed
4. **Styling Issues**: Check Tailwind CSS configuration

### Getting Help
- Check the GitHub issues for common problems
- Review the Next.js documentation
- Consult Supabase documentation for database issues

## 📄 License

This project is created for S.N. Pvt. Industrial Training Institute. All rights reserved.

## 🤝 Contributing

This is a private project for the institute. For any modifications or improvements, please contact the development team.

---

**Built with ❤️ for Excellence in Technical Education**
