-- S.N. ITI Website Database Schema
-- This file contains the SQL schema for the website's database
-- Use this with Supabase or any PostgreSQL database

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table for admin authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'admin' CHECK (role IN ('admin', 'editor')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- Pages table for website content
CREATE TABLE pages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Media files table
CREATE TABLE media_files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    alt_text TEXT,
    file_type VARCHAR(100) NOT NULL,
    file_size INTEGER NOT NULL,
    upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    uploaded_by UUID REFERENCES users(id)
);

-- Site settings table
CREATE TABLE site_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id)
);

-- Navigation menu table
CREATE TABLE navigation_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    url VARCHAR(255) NOT NULL,
    parent_id UUID REFERENCES navigation_items(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Contact form submissions table
CREATE TABLE contact_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    subject VARCHAR(255),
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET
);

-- Create indexes for better performance
CREATE INDEX idx_pages_slug ON pages(slug);
CREATE INDEX idx_pages_published ON pages(is_published);
CREATE INDEX idx_pages_created_at ON pages(created_at);
CREATE INDEX idx_media_files_type ON media_files(file_type);
CREATE INDEX idx_media_files_upload_date ON media_files(upload_date);
CREATE INDEX idx_navigation_parent ON navigation_items(parent_id);
CREATE INDEX idx_navigation_sort ON navigation_items(sort_order);
CREATE INDEX idx_contact_submitted_at ON contact_submissions(submitted_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_pages_updated_at BEFORE UPDATE ON pages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_site_settings_updated_at BEFORE UPDATE ON site_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_navigation_items_updated_at BEFORE UPDATE ON navigation_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin user (password: admin123)
-- Note: In production, use a secure password and proper hashing
INSERT INTO users (email, password_hash, role) VALUES 
('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukqOQ/YGC', 'admin');

-- Insert default site settings
INSERT INTO site_settings (key, value, description) VALUES 
('site_name', 'S.N. Pvt. Industrial Training Institute', 'Website name'),
('site_description', 'Excellence in Technical Education & Skill Development', 'Website description'),
('contact_email', '<EMAIL>', 'Primary contact email'),
('contact_phone', '01564-275628', 'Primary contact phone'),
('contact_mobile', '+91-9414947801', 'Primary contact mobile'),
('address', 'D-117, Kaka Colony, Gandhi Vidhya Mandir, Teh.-Sardar Shahar, Dist. Churu', 'Institute address'),
('established_year', '2009', 'Year of establishment'),
('ncvt_affiliation', '2009', 'NCVT affiliation year'),
('sanctioned_seats', '126', 'Total sanctioned seats'),
('primary_trade', 'Electrician', 'Primary trade offered');

-- Insert default pages
INSERT INTO pages (title, slug, content, meta_title, meta_description, is_published) VALUES 
('Home', 'home', '<h1>Welcome to S.N. Pvt ITI</h1><p>Excellence in Technical Education & Skill Development</p>', 'S.N. Pvt. Industrial Training Institute | Home', 'Welcome to S.N. Pvt. Industrial Training Institute - Excellence in Technical Education & Skill Development', true),
('About Institute', 'about-institute', '<h1>About Institute</h1><p>S.N. Pvt. Industrial Training Institute was established in 2009...</p>', 'About Institute | S.N. ITI', 'Learn about S.N. Pvt. Industrial Training Institute - our history, mission, and commitment to excellence', true),
('Contact', 'contact', '<h1>Contact Us</h1><p>Get in touch with us for admissions and inquiries.</p>', 'Contact Us | S.N. ITI', 'Contact S.N. Pvt. Industrial Training Institute for admissions, inquiries, and assistance', true);

-- Insert default navigation items
INSERT INTO navigation_items (title, url, sort_order) VALUES 
('Home', '/', 1),
('About Us', '/about', 2),
('Admissions', '/admissions', 3),
('Facilities', '/facilities', 4),
('Trainee', '/trainee', 5),
('Staff', '/staff', 6),
('More', '/more', 7),
('Gallery', '/gallery', 8),
('Contact', '/contact', 9);

-- Row Level Security (RLS) policies for Supabase
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE navigation_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;

-- Create policies (adjust based on your authentication setup)
-- These are basic policies - customize based on your needs

-- Users policies (admin only)
CREATE POLICY "Users are viewable by authenticated users" ON users
    FOR SELECT USING (auth.role() = 'authenticated');

-- Pages policies (public read, admin write)
CREATE POLICY "Pages are viewable by everyone" ON pages
    FOR SELECT USING (is_published = true OR auth.role() = 'authenticated');

CREATE POLICY "Pages are editable by authenticated users" ON pages
    FOR ALL USING (auth.role() = 'authenticated');

-- Media files policies (public read, admin write)
CREATE POLICY "Media files are viewable by everyone" ON media_files
    FOR SELECT USING (true);

CREATE POLICY "Media files are editable by authenticated users" ON media_files
    FOR ALL USING (auth.role() = 'authenticated');

-- Site settings policies (public read, admin write)
CREATE POLICY "Site settings are viewable by everyone" ON site_settings
    FOR SELECT USING (true);

CREATE POLICY "Site settings are editable by authenticated users" ON site_settings
    FOR ALL USING (auth.role() = 'authenticated');

-- Navigation items policies (public read, admin write)
CREATE POLICY "Navigation items are viewable by everyone" ON navigation_items
    FOR SELECT USING (is_active = true);

CREATE POLICY "Navigation items are editable by authenticated users" ON navigation_items
    FOR ALL USING (auth.role() = 'authenticated');

-- Contact submissions policies (admin only)
CREATE POLICY "Contact submissions are viewable by authenticated users" ON contact_submissions
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Contact submissions are insertable by everyone" ON contact_submissions
    FOR INSERT WITH CHECK (true);
