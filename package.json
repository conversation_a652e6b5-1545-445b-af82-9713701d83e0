{"name": "snpitc-website", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "@supabase/supabase-js": "^2.45.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@tinymce/tinymce-react": "^5.1.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "react-hook-form": "^7.52.1", "react-hot-toast": "^2.4.1", "lucide-react": "^0.427.0", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0", "framer-motion": "^11.3.19", "embla-carousel-react": "^8.1.8", "react-intersection-observer": "^9.13.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "@tailwindcss/typography": "^0.5.13", "@tailwindcss/forms": "^0.5.7", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}