{"name": "snpitc-website", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.45.0", "@tinymce/tinymce-react": "^6.2.0", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "embla-carousel-react": "^8.1.8", "framer-motion": "^11.3.19", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.427.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.52.1", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.13.0", "tailwind-merge": "^2.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.13", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}