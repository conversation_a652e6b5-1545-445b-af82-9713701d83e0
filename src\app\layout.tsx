import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Toaster } from 'react-hot-toast';
import Layout from '@/components/layout/Layout';
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "S.N. Pvt. Industrial Training Institute | Technical Education Excellence",
  description: "S.N. Pvt. Industrial Training Institute provides world-class technical education and skill development programs. Approved by Govt. of Rajasthan and affiliated to NCVT since 2009.",
  keywords: "ITI, Industrial Training, Technical Education, NCVT, Rajasthan, Skill Development, Electrician, Vocational Training",
  authors: [{ name: "S.N. Pvt. Industrial Training Institute" }],
  creator: "S.N. Pvt. Industrial Training Institute",
  publisher: "S.N. Pvt. Industrial Training Institute",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: process.env.NEXT_PUBLIC_SITE_URL,
    siteName: "S.N. Pvt. Industrial Training Institute",
    title: "S.N. Pvt. Industrial Training Institute | Technical Education Excellence",
    description: "World-class technical education and skill development programs. Approved by Govt. of Rajasthan and affiliated to NCVT since 2009.",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "S.N. Pvt. Industrial Training Institute",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "S.N. Pvt. Industrial Training Institute",
    description: "World-class technical education and skill development programs.",
    images: ["/images/og-image.jpg"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased">
        <Layout>
          {children}
        </Layout>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
      </body>
    </html>
  );
}
