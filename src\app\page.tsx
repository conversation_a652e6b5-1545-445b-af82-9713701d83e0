import Image from "next/image";
import Link from "next/link";
import { ArrowRight, Users, Award, Building, BookOpen } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-primary-600 to-primary-800 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                Welcome to <span className="text-yellow-300">SN Pvt ITI</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100">
                Excellence in Technical Education & Skill Development
              </p>
              <p className="text-lg text-blue-50 leading-relaxed">
                S.N. Pvt. Industrial Training Institute welcomes you with Job oriented Industrial Training Courses.
                These courses help trainees for employment in public or private sector & Self employment.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold">
                  <Link href="/admissions" className="flex items-center">
                    Apply Now <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-primary-600">
                  <Link href="/about">Learn More</Link>
                </Button>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h3 className="text-2xl font-semibold mb-6">Quick Facts</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Award className="h-6 w-6 text-yellow-300" />
                    <span>Established in 2009</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Building className="h-6 w-6 text-yellow-300" />
                    <span>NCVT Affiliated since 2009</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Users className="h-6 w-6 text-yellow-300" />
                    <span>126 Sanctioned Seats</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <BookOpen className="h-6 w-6 text-yellow-300" />
                    <span>Electrician Trade Specialist</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Image Slideshow Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Campus & Facilities
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Take a virtual tour of our modern facilities and see where excellence in technical education comes to life.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {[1, 2, 3, 4, 5].map((num) => (
              <div key={num} className="relative group cursor-pointer">
                <Image
                  src={`/images/slideshow/0${num}.jpg`}
                  alt={`Campus facility ${num}`}
                  width={300}
                  height={200}
                  className="w-full h-48 object-cover rounded-lg shadow-md group-hover:shadow-xl transition-shadow duration-300"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center">
                  <span className="text-white font-semibold">View Gallery</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Introduction
              </h2>
              <div className="prose prose-lg text-gray-600">
                <p className="mb-4">
                  S.N. Pvt. Industrial Training Institute was established in 2009. At present institute having a
                  total capacity of 126 sanction seats in Electrician trade affiliated to NCVT.
                </p>
                <p className="mb-6">
                  Nav Chetana Shikshan Sansthan has taken up skill development as one of its major focus area for
                  empowering youth with industry-relevant skills and knowledge.
                </p>
                <Link href="/about/introduction">
                  <Button variant="outline">
                    Read More <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>

            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Vision & Mission
              </h2>
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-primary-600">VISION</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      To be recognized as an Excellent Organization Providing World Class Technical Education at all Levels.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-primary-600">MISSION</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      To Strive for Excellence in Technical Education and prepare skilled professionals for the industry.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Contact Information
            </h2>
            <p className="text-lg text-gray-600">
              Get in touch with us for admissions, inquiries, or any assistance
            </p>
          </div>

          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center text-2xl">S.N. Industrial Training Centre</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center space-y-2">
                <p className="font-semibold">Address:</p>
                <p className="text-gray-600">
                  D-117, Kaka Colony, Gandhi Vidhya Mandir,<br />
                  Teh.-Sardar Shahar, Dist. Churu
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                <div className="text-center">
                  <p className="font-semibold">Phone:</p>
                  <p className="text-gray-600">01564-275628</p>
                </div>
                <div className="text-center">
                  <p className="font-semibold">Mobile:</p>
                  <p className="text-gray-600">+91-9414947801</p>
                </div>
              </div>

              <div className="text-center pt-4">
                <p className="font-semibold">Email:</p>
                <a href="mailto:<EMAIL>" className="text-primary-600 hover:underline">
                  <EMAIL>
                </a>
              </div>

              <div className="text-center pt-4">
                <Link href="/contact">
                  <Button>
                    View Full Contact Details <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
